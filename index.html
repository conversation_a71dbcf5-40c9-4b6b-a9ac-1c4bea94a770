<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lumina Photography Studio - Professional Photography Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a1a1a',
                        secondary: '#f8f8f8',
                        accent: '#d4af37',
                        muted: '#6b7280'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        html { scroll-behavior: smooth; }
        .hero-bg { background: linear-gradient(135deg, rgba(26,26,26,0.7), rgba(26,26,26,0.5)), url('https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'); background-size: cover; background-position: center; }
        .fade-in { opacity: 0; transform: translateY(30px); transition: all 0.8s ease; }
        .fade-in.visible { opacity: 1; transform: translateY(0); }
        .hover-scale { transition: transform 0.3s ease; }
        .hover-scale:hover { transform: scale(1.05); }
        .nav-blur { backdrop-filter: blur(10px); background: rgba(255,255,255,0.95); }
        .gallery-item { transition: all 0.3s ease; }
        .gallery-item:hover { transform: translateY(-10px); box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="font-sans text-primary bg-white">
    <!-- Navigation -->
    <nav id="navbar" class="fixed top-0 w-full z-50 transition-all duration-300 py-4">
        <div class="container mx-auto px-6 flex justify-between items-center">
            <div class="text-2xl font-serif font-bold">Lumina</div>
            <div class="hidden md:flex space-x-8">
                <a href="#home" class="nav-link hover:text-accent transition-colors">Home</a>
                <a href="#services" class="nav-link hover:text-accent transition-colors">Services</a>
                <a href="#about" class="nav-link hover:text-accent transition-colors">About</a>
                <a href="#contact" class="nav-link hover:text-accent transition-colors">Contact</a>
            </div>
            <button id="mobile-menu-btn" class="md:hidden text-2xl">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white shadow-lg">
            <div class="px-6 py-4 space-y-4">
                <a href="#home" class="block hover:text-accent transition-colors">Home</a>
                <a href="#services" class="block hover:text-accent transition-colors">Services</a>
                <a href="#about" class="block hover:text-accent transition-colors">About</a>
                <a href="#contact" class="block hover:text-accent transition-colors">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-bg min-h-screen flex items-center justify-center text-white">
        <div class="text-center px-6 max-w-4xl">
            <h1 class="text-5xl md:text-7xl font-serif font-bold mb-6 fade-in">
                Capturing Life's<br>
                <span class="text-accent">Perfect Moments</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 fade-in font-light">
                Professional photography services that transform your precious memories into timeless art
            </p>
            <div class="space-x-4 fade-in">
                <button class="bg-accent text-primary px-8 py-4 rounded-full font-semibold hover:bg-yellow-400 transition-colors hover-scale">
                    View Portfolio
                </button>
                <button class="border-2 border-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-primary transition-colors">
                    Book Session
                </button>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-secondary">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4">Our Services</h2>
                <p class="text-xl text-muted max-w-2xl mx-auto">
                    From intimate portraits to grand celebrations, we specialize in capturing the essence of every moment
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4">Wedding Photography</h3>
                        <p class="text-muted mb-6">Elegant and timeless wedding photography that captures the magic of your special day with artistic flair.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>
                
                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4">Portrait Sessions</h3>
                        <p class="text-muted mb-6">Professional portraits that reveal your unique personality and style in stunning, magazine-quality images.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>
                
                <div class="gallery-item bg-white rounded-2xl overflow-hidden shadow-lg fade-in">
                    <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')"></div>
                    <div class="p-8">
                        <h3 class="text-2xl font-serif font-semibold mb-4">Event Photography</h3>
                        <p class="text-muted mb-6">Comprehensive event coverage that documents every important moment with creativity and professionalism.</p>
                        <a href="#" class="text-accent font-semibold hover:underline">Learn More →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div class="fade-in">
                    <h2 class="text-4xl md:text-5xl font-serif font-bold mb-6">Meet the Artist</h2>
                    <p class="text-lg text-muted mb-6">
                        With over a decade of experience in professional photography, I specialize in creating stunning visual narratives that capture the authentic emotions and beauty of life's most precious moments.
                    </p>
                    <p class="text-lg text-muted mb-8">
                        My approach combines technical excellence with artistic vision, ensuring that every photograph tells a unique story and becomes a treasured memory for years to come.
                    </p>
                    <div class="flex space-x-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent">500+</div>
                            <div class="text-muted">Happy Clients</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent">10+</div>
                            <div class="text-muted">Years Experience</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-accent">50+</div>
                            <div class="text-muted">Awards Won</div>
                        </div>
                    </div>
                </div>
                <div class="fade-in">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" 
                         alt="Professional Photographer" 
                         class="rounded-2xl shadow-2xl hover-scale">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-primary text-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl md:text-5xl font-serif font-bold mb-4">Let's Create Together</h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Ready to capture your special moments? Get in touch and let's discuss how we can bring your vision to life.
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-16">
                <div class="fade-in">
                    <h3 class="text-2xl font-serif font-semibold mb-6">Get In Touch</h3>
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-phone text-accent text-xl"></i>
                            <span class="text-lg">+****************</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-envelope text-accent text-xl"></i>
                            <span class="text-lg"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <i class="fas fa-map-marker-alt text-accent text-xl"></i>
                            <span class="text-lg">123 Photography Lane, Creative City, CC 12345</span>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-2xl hover:text-accent transition-colors"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-2xl hover:text-accent transition-colors"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                
                <div class="fade-in">
                    <form class="space-y-6">
                        <div>
                            <input type="text" placeholder="Your Name" 
                                   class="w-full px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:border-accent focus:outline-none transition-colors">
                        </div>
                        <div>
                            <input type="email" placeholder="Your Email" 
                                   class="w-full px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:border-accent focus:outline-none transition-colors">
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:border-accent focus:outline-none transition-colors">
                                <option>Select Service</option>
                                <option>Wedding Photography</option>
                                <option>Portrait Session</option>
                                <option>Event Photography</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <textarea placeholder="Tell us about your project..." rows="4"
                                      class="w-full px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:border-accent focus:outline-none transition-colors resize-none"></textarea>
                        </div>
                        <button type="submit" 
                                class="w-full bg-accent text-primary py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors hover-scale">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <div class="text-2xl font-serif font-bold mb-4">Lumina</div>
            <p class="text-gray-400 mb-4">© 2024 Lumina Photography Studio. All rights reserved.</p>
            <p class="text-gray-400">Crafting memories, one frame at a time.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Navbar background on scroll
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('nav-blur');
            } else {
                navbar.classList.remove('nav-blur');
            }
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We\'ll get back to you soon.');
            this.reset();
        });
    </script>
</body>
</html>
